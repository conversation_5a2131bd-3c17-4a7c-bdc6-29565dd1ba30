import { cn } from '@/components/lib/utils'
import { SelectFilesParams } from '@app/shared/types/ipc/upload-task'
import { FolderPlus, UploadIcon } from 'lucide-react'
import React, { FC, useState } from 'react'
import { createFileFilters, getFileType } from '@app/shared/file'
import { useAuthStatus } from '@/components/auth/AuthGuard'
import { UploadModule } from '@app/shared/types/ipc/file-uploader'
import { UploadTask } from '@app/shared/types/database.types'
import { useUploadTasksContext } from '@/contexts/upload-task/context'
import { ResourceSource } from '@/types/resources'

export interface FileOrFolderUploaderProps {
  folderUuid?: string,
  resourceSource: ResourceSource
}

export const FileOrFolderUploader: FC<FileOrFolderUploaderProps> = ({
  folderUuid,
  resourceSource = ResourceSource.MEDIA
}) => {
  const { selectFiles, createTask, uploadFromPath, uploadFolder, isUploadingFolder } = useUploadTasksContext()
  const { authData, teamId } = useAuthStatus()
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSelectFiles = async(data: SelectFilesParams) => {
    const filePaths = await selectFiles(data)

    if (!filePaths.length || !authData?.accessToken || !teamId) {
      return
    }

    if (!data.folder) {
      try {
        const tasks: UploadTask.IUploadTask[] = []

        for (const filePath of filePaths) {
          const fileName = filePath.split(/[/\\]/).pop() || 'unknown'

          // 创建上传任务
          const task = await createTask({
            uid: authData.userId.toString(),
            team_id: teamId,
            name: fileName,
            local_path: filePath,
            type: getFileType(fileName),
            folder_id: folderUuid,
            upload_module: UploadModule.media
          })

          tasks.push(task)

          try {
            console.log(`开始上传文件: ${fileName}`)

            const uploadResult = await uploadFromPath({
              taskId: task.id,
              filePath: filePath
            })

            if (uploadResult.success) {
              console.log(`文件 ${fileName} 上传成功:`, uploadResult.url)
            } else {
              console.error(`文件 ${fileName} 上传失败:`, uploadResult.error)
            }
          } catch (uploadError) {
            console.error(`上传文件 ${fileName} 时出错:`, uploadError)
          }
        }

        console.log(`成功创建并开始上传 ${tasks.length} 个任务`)
      } catch (error) {
        console.error('创建上传任务失败:', error)
      }
    } else {
      // 文件夹上传逻辑
      try {
        setIsProcessing(true)

        // 调用文件夹上传功能
        const result = await uploadFolder({
          folderPath: filePaths[0],
          parentFolderUuid: folderUuid || '',
          resourceType: resourceSource,
          uploadModule: UploadModule.media,
          uid: authData.userId.toString(),
          teamId: teamId,
          maxSize: 100 * 1024 * 1024
        })

        if (result.success) {
          console.log(`文件夹上传成功，共 ${result.totalFiles} 个文件`)
        } else {
          console.error(`文件夹上传失败: ${result.error}`)
        }
      } catch (error) {
        console.error('文件夹上传过程中发生错误:', error)
      } finally {
        setIsProcessing(false)
      }
    }
  }

  return (
    <div
      className={cn(
        'relative flex items-center justify-center border-dashed border-2 border-gray-200 rounded-lg text-sm cursor-pointer overflow-hidden transition-all duration-300 w-50 h-50 group',
      )}
    >
      {/* 默认显示的“上传素材” */}
      <div
        className={cn(
          'flex flex-col items-center justify-center transition-all duration-300 group-hover:opacity-0 group-hover:scale-95',
        )}
      >
        <span className="text-3xl text-gray-400 mb-2">+</span>
        <span className="text-gray-500">上传素材</span>
      </div>

      <div
        className={cn(
          'absolute inset-0 text-gray-500 flex flex-col transition-all duration-300 origin-center opacity-0 scale-y-0 pointer-events-none',
          'group-hover:opacity-100 group-hover:scale-y-100 group-hover:pointer-events-auto'
        )}
      >
        <div
          onClick={() => handleSelectFiles({
            multiple: true,
            filters: createFileFilters(['VIDEO', 'AUDIO', 'IMAGE']),
          })}
          className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors"
        >
          <UploadIcon className="size-4" />
          <div>上传文件</div>
        </div>

        <div
          onClick={() => handleSelectFiles({
            multiple: false,
            folder: true,
            filters: createFileFilters(['VIDEO', 'AUDIO', 'IMAGE']),
            uploadModule: UploadModule.media
          })}
          className={cn(
            'flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors',
            (isProcessing || isUploadingFolder) && 'opacity-50 cursor-not-allowed'
          )}
        >
          <FolderPlus className="size-4" />
          <div>{(isProcessing || isUploadingFolder) ? '上传中...' : '上传文件夹'}</div>
        </div>
      </div>

      {/* 中间的虚线分割线 */}
      <div
        className={cn(
          'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full border-b-2 border-dashed border-gray-200 opacity-0 transition-opacity duration-300',
          'group-hover:opacity-100'
        )}
      />
    </div>

  )
}
