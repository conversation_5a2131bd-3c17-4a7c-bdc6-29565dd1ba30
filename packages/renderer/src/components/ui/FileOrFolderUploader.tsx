import { cn } from '@/components/lib/utils'
import { FolderUploadParams, SelectFilesParams, StorageFolderUploadParams, TeamFolderUploadParams } from '@app/shared/types/ipc/upload-task'
import { ChevronDown, FolderPlus, UploadCloudIcon, UploadIcon } from 'lucide-react'
import React, { FC, useEffect } from 'react'
import { createFileFilters, FileCategory, getFileType } from '@app/shared/file'
import { useAuthStatus } from '@/components/auth/AuthGuard'
import { UploadModule } from '@app/shared/types/ipc/file-uploader'
import { useUploadTasksContext } from '@/contexts/upload-task/context'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'

export interface FileOrFolderUploaderProps  {
  folderUuid?: string,
  folderOptions: TeamFolderUploadParams | StorageFolderUploadParams
  variant: 'card' | 'popover',
  fileCategory?: FileCategory[],
  onComplete: (payload: any) => void
}

interface UploaderComponentProps {
  onFilesUpload: () => Promise<void>
  onFolderUpload: () => Promise<void>
}

const PopoverUploader: FC<UploaderComponentProps> = ({
  onFilesUpload,
  onFolderUpload
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="w-36 border-0 bg-primary/10 flex justify-between gap-1">
          <span>
            上传
            {/* {isUploading && <span>({`上传进度：${uploadProgress}%`})</span>} */}
          </span>
          <ChevronDown className="w-3.5 h-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-36 p-0">
        <div className="py-1">
          <button 
            onClick={onFilesUpload} 
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2"
          >
            <UploadCloudIcon />
            上传文件
          </button>

          <button 
            onClick={onFolderUpload}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2"
          >
            <FolderPlus  />
            文件夹上传
          </button>
        </div>
      </PopoverContent>
    </Popover>
  )
}

const CardUploader: FC<UploaderComponentProps> = ({
  onFilesUpload,
  onFolderUpload
}) => {
  return (
    <div
      className={cn(
        'relative flex items-center justify-center border-dashed border-2 border-gray-200 rounded-lg text-sm cursor-pointer overflow-hidden transition-all duration-300 w-50 h-50 group',
      )}
    >
      {/* 默认显示的“上传素材” */}
      <div
        className={cn(
          'flex flex-col items-center justify-center transition-all duration-300 group-hover:opacity-0 group-hover:scale-95',
        )}
      >
        <span className="text-3xl text-gray-400 mb-2">+</span>
        <span className="text-gray-500">上传素材</span>
      </div>

      <div
        className={cn(
          'absolute inset-0 text-gray-500 flex flex-col transition-all duration-300 origin-center opacity-0 scale-y-0 pointer-events-none',
          'group-hover:opacity-100 group-hover:scale-y-100 group-hover:pointer-events-auto'
        )}
      >
        <div
          onClick={onFilesUpload}
          className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors"
        >
          <UploadIcon className="size-4" />
          <div>上传文件</div>
        </div>

        <div
          onClick={onFolderUpload}
          className={cn(
            'flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors',
          )}
        >
          <FolderPlus className="size-4" />
          <div>上传文件夹</div>
        </div>
      </div>

      {/* 中间的虚线分割线 */}
      <div
        className={cn(
          'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full border-b-2 border-dashed border-gray-200 opacity-0 transition-opacity duration-300',
          'group-hover:opacity-100'
        )}
      />
    </div>

  )
}

export const FileOrFolderUploader: FC<FileOrFolderUploaderProps> = ({
  folderUuid,
  variant = 'card',
  fileCategory = ['MEDIA'],
  folderOptions,
  onComplete
}) => {
  const { selectFiles, createTask, uploadFromPath, uploadFolder, isUploadingFolder, subscribe, unsubscribe } = useUploadTasksContext()
  const { authData, teamId } = useAuthStatus()

  const handleUploadFiles = async(filePaths: string[]) => {
    if (!filePaths.length || !authData?.accessToken || !teamId) {
      return
    }
    const taskIds: number[] = []

    try {
      for (const filePath of filePaths) {
        const fileName = filePath.split(/[/\\]/).pop() || 'unknown'

        // 创建上传任务
        const task = await createTask({
          uid: authData.userId.toString(),
          team_id: teamId,
          name: fileName,
          local_path: filePath,
          type: getFileType(fileName),
          folder_id: folderUuid,
          upload_module: UploadModule.media
        })

        taskIds.push(task.id)
      }
    } catch (error) {
      console.error('创建上传任务失败:', error)
    }
    
    try {
      await uploadFromPath({ taskIds })
    } catch (uploadError) {
      console.error('上传文件出错:', uploadError)
    }
  }

  const handleUploadFolder = async(filePaths: string[]) => {
    if (!filePaths.length || !authData?.accessToken || !teamId) {
      return
    }
    try {
      const params: FolderUploadParams = {
        folderPath: filePaths[0],
        parentFolderUuid: folderUuid || '',
        uploadModule: UploadModule.media,
        uid: authData.userId.toString(),
        teamId: teamId,
        maxSize: 100 * 1024 * 1024,
        folderOptions
      }
      const result = await uploadFolder(params)

      if (result.success) {
        console.log(`文件夹上传成功，共 ${result.tasks?.length} 个文件`)
      } else {
        console.error(`文件夹上传失败: ${result.error}`)
      }
    } catch (error) {
      console.error('文件夹上传过程中发生错误:', error)
    } 
  }

  const handleSelectFiles = async(data: SelectFilesParams) => {
    const filePaths = await selectFiles(data)
    
    if (!data.folder) {
      await handleUploadFiles(filePaths)
    } else {
      await handleUploadFolder(filePaths)
    }
  }

  useEffect(() => {
    const cb = (payload: any) => onComplete?.(payload)
    subscribe('batch-upload-complete', cb)
    return () => unsubscribe('batch-upload-complete', cb)
  }, [])

  if (variant === 'card') return (
    <CardUploader
      onFilesUpload={() => handleSelectFiles({
        multiple: true,
        filters: createFileFilters(fileCategory),
      })}
      onFolderUpload={() => handleSelectFiles({
        multiple: false,
        folder: true,
        filters: createFileFilters(fileCategory),
        uploadModule: UploadModule.media
      })}
    />
  )

  if (variant === 'popover') return (
    <PopoverUploader
      onFilesUpload={() => handleSelectFiles({
        multiple: true,
        filters: createFileFilters(fileCategory),
      })}
      onFolderUpload={() => handleSelectFiles({
        multiple: false,
        folder: true,
        filters: createFileFilters(fileCategory),
        uploadModule: UploadModule.media
      })}
    />
  )

  else return null
}
