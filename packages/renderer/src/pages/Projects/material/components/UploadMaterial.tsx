import React, { useState } from 'react'
import { cn } from '@/components/lib/utils'
import { FileUploader, FileUploaderRenderProps, UploadedFile } from '@/components/ui/file-uploader'
import { FolderUploadedFile, FolderUploader } from '@/components/ui/folder-uploader'
import { ChevronDown, FolderPlus, UploadIcon } from 'lucide-react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { ResourceSource } from '@/types/resources'

interface UnifiedUploaderProps {
  folderUuid: string
  orientation?: 'horizontal' | 'vertical' // 用于card模式控制布局
  mode?: 'card' | 'popover' // 选择展示模式
  resourceType?: ResourceSource
  fileUploadTypes?: string[]
  onUpload?: (uploaded: (FolderUploadedFile | UploadedFile)[]) => void
  buttonLabel?: string // 弹出菜单按钮文字
  customFileUploaderRender?: (props: FileUploaderRenderProps) => React.ReactNode
}

export const handleFileChange = (
  files: any[],
  setIsUploading: React.Dispatch<React.SetStateAction<boolean>>,
  setUploadProgress: React.Dispatch<React.SetStateAction<number>>,
) => {
  if (files.length > 0) {
    const uploadingFiles = files.filter(f => f.status === 'uploading')
    setIsUploading(uploadingFiles.length > 0)

    const totalProgress = files.reduce((sum, f) => sum + (f.progress || 0), 0)
    const avgProgress = totalProgress / files.length
    setUploadProgress(Math.round(avgProgress * 100))
  } else {
    setIsUploading(false)
    setUploadProgress(0)
  }
}

const DefaultFileUploader = ({
  getRootProps,
  getInputProps,
  isLoading,
  mode,
}: FileUploaderRenderProps & { mode: string }) => {
  const iconSizeClass = mode === 'card' ? 'w-5 h-5' : 'w-3.5 h-3.5'
  return (
    <div className="flex items-center justify-center gap-2">
      <UploadIcon className={iconSizeClass} />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '上传文件'}
      </div>
    </div>
  )
}

const UploadMaterial: React.FC<UnifiedUploaderProps> = ({
  folderUuid,
  orientation = 'horizontal',
  mode = 'popover',
  resourceType = ResourceSource.MEDIA,
  fileUploadTypes,
  onUpload,
  buttonLabel = '上传',
  customFileUploaderRender,
}) => {
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [hovered, setHovered] = useState(false)

  // 上传文件的回调
  const onFileChange = (files: any[]) => {
    handleFileChange(files, setIsUploading, setUploadProgress)
  }

  // 上传完成回调
  const onUploadComplete = (files: (FolderUploadedFile | UploadedFile)[]) => {
    setTimeout(() => setIsUploading(false), 1000)
    onUpload?.(files)
  }

  const renderFileUploader = () => (
    <FileUploader
      maxSize={1.5 * 1024 * 1024 * 1024}
      folderUuid={folderUuid}
      fileTypes={fileUploadTypes}
      renderCustomComponent={props => (
        customFileUploaderRender
          ? customFileUploaderRender(props)
          : <DefaultFileUploader {...props} mode={mode} />
      )}
      onChange={onFileChange}
      onUpload={onUploadComplete}
    />
  )

  const renderFolderUploader = () => (
    <FolderUploader
      maxSize={1.5 * 1024 * 1024 * 1024}
      resourceType={resourceType}
      fileTypes={fileUploadTypes}
      folderUuid={folderUuid}
      children={
        <div className="flex items-center justify-center">
          <FolderPlus className="w-5 h-5 mr-2" />
          文件夹上传
        </div>
      }
      isShowUploadedFiles={false}
      showFileList={false}
      onProgress={(current, total) => {
        if (current === 1) setIsUploading(true)
        if (current === total) {
          setTimeout(() => setIsUploading(false), 1000)
          onUpload?.([])
        }
      }}
      onUpload={onUpload}
    />
  )

  if (mode === 'card') {
    return (
      <div
        className={cn(
          'relative flex items-center justify-center border-dashed border-2 border-gray-200 rounded-lg text-sm cursor-pointer overflow-hidden transition-all duration-300',
          orientation === 'horizontal' ? 'w-50 h-50' : 'w-40 h-64',
        )}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        <div
          className={cn(
            'flex flex-col items-center justify-center transition-opacity duration-300',
            hovered ? 'opacity-0 scale-95' : 'opacity-100 scale-100',
          )}
        >
          <span className="text-3xl text-gray-400 mb-2">+</span>
          <span className="text-gray-500">上传素材</span>
        </div>
        {isUploading && <div className={cn('absolute bottom-0 left-1')}>上传进度：{uploadProgress}%</div>}

        <div
          className={cn(
            'absolute inset-0 text-gray-500 flex flex-col transition-all duration-300',
            hovered ? 'opacity-100 scale-y-100' : 'opacity-0 scale-y-0 pointer-events-none',
            'origin-center',
          )}
        >
          <div className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors">
            {renderFileUploader()}
          </div>

          <button className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors">
            {renderFolderUploader()}
          </button>
        </div>
        <div
          className={cn(
            'absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full border-b-2 border-dashed border-gray-200',
            hovered ? 'opacity-100' : 'opacity-0',
          )}
        />
      </div>
    )
  } else {
    // 弹出菜单模式
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="w-36 border-0 bg-primary/10 flex justify-between gap-1">
            <span>
              {buttonLabel}
              {isUploading && <span>({`上传进度：${uploadProgress}%`})</span>}
            </span>
            <ChevronDown className="w-3.5 h-3.5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-36 p-0">
          <div className="py-1">
            <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
              {renderFileUploader()}
            </button>

            <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
              {renderFolderUploader()}
            </button>
          </div>
        </PopoverContent>
      </Popover>
    )
  }
}
export default UploadMaterial
