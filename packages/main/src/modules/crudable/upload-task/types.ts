/**
 * 上传任务模块的共享类型定义
 */

/**
 * OSS签名响应接口
 */
export interface STSSignature {
  accessKeyId: string
  secretAccessKey: string
  securityToken: string
  expiration: string
  platform: number
  callback: string
  callbackVar: string
  objectKey: string
  endpoint: string
  secure: boolean
  regionId: string
  bucket: string
  body: any
  objectId: string
}

/**
 * 解密后的callback配置接口
 */
export interface CallbackConfig {
  callbackUrl: string
  callbackBody: string
  callbackBodyType: string
}

/**
 * 解密后的callbackVar配置接口
 */
export interface CallbackVarConfig {
  bizId: string
  md5Hash: string
  name: string
  ntrRegion: string
  rat: string
  region: string
  rid: string
  sign: string
  source: string
  tid: string
  uid: string
}

/**
 * Worker任务数据接口
 */
export interface WorkerTaskData {
  taskId: number
  filePath: string
  fileName: string
  uploadConfig: {
    folderUuid?: string
    fileMd5?: string
    module: string
    partSize: number
    checkpointData?: string  // 断点续传数据
  }
}

/**
 * Worker消息接口
 */
export interface WorkerMessage {
  type: 'upload' | 'pause' | 'resume' | 'cancel'
  taskId?: number
  data?: any
}

/**
 * OSS签名请求参数
 */
export interface OSSSignatureParams {
  module: string
  fileName: string
  folderUuid?: string
  fileMd5?: string
}
