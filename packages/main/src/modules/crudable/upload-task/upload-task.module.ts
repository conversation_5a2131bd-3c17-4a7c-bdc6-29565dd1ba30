import { Modu<PERSON> } from '@nestjs/common'
import { UploadTaskIPCHandler } from './upload-task.ipc-handler.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { UploadTaskService } from './upload-task.service.js'
import { GlobalModule } from '../../global/global.module.js'
import { UploadQueueManager } from './upload-queue-manager.js'
import { OSSSignatureService } from './oss-signature.service.js'

@Module({
  imports: [GlobalModule],
  providers: [
    UploadTaskRepository,
    UploadTaskService,
    UploadTaskIPCHandler,
    UploadQueueManager,
    OSSSignatureService,
  ],
  exports: [
    UploadTaskService,
    UploadQueueManager,
    OSSSignatureService,
  ]
})
export class UploadTaskModule {}
