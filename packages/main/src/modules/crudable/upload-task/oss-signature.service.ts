import { Injectable, Logger } from '@nestjs/common'
import { RequestService } from '@/modules/global/request.service.js'
import { STSSignature, OSSSignatureParams } from './types.js'

/**
 * OSS签名服务
 * 专门负责获取OSS上传签名，从FileUploaderService中解耦出来
 */
@Injectable()
export class OSSSignatureService {

  private readonly logger = new Logger('OSSSignatureService')

  constructor(
    private readonly requestService: RequestService
  ) {}

  /**
   * 生成OSS上传签名
   * @param params 签名参数
   * @returns OSS签名信息
   */
  async generateSignature(params: OSSSignatureParams): Promise<STSSignature> {
    try {
      const { module, fileName, folderUuid = '', fileMd5 = '' } = params

      const urlParams = new URLSearchParams({ fileName })

      if (module !== 'publishing') {
        urlParams.append('folderUuid', folderUuid)
      }
      urlParams.append('fileMd5', fileMd5)

      const url = `/app-api/creative/oss/${module}/upload-param?${urlParams.toString()}`

      this.logger.debug(`正在从后端获取OSS签名: ${url}`)

      const responseData = await this.requestService.get(url)

      if (!responseData) {
        throw new Error('API响应格式错误：缺少data字段')
      }

      return responseData as STSSignature
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      this.logger.error('生成OSS预签名URL失败:', errorMsg)
      throw new Error(errorMsg)
    }
  }

  /**
   * 生成文件名（添加时间戳避免重复）
   * @param originalName 原始文件名
   * @returns 处理后的文件名
   */
  generateFileName(originalName: string): string {
    const timestamp = Date.now()
    const extension = originalName.split('.').pop()
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
    return `${nameWithoutExt}_${timestamp}.${extension}`
  }
}
